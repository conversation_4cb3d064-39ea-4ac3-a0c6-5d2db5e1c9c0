"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Search, Eye, Edit, Trash2, AlertTriangle, Package } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import CustomPagination from "@/components/ui/custom-pagination";
import Link from "next/link";

interface StockBatch {
  id: string;
  productId: string;
  productSupplierId: string;
  batchNumber: string | null;
  receivedDate: string;
  expiryDate: string | null;
  quantity: number;
  remainingQuantity: number;
  purchasePrice: number;
  status: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT";
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  product: {
    id: string;
    name: string;
    sku: string;
    unit: {
      id: string;
      name: string;
      abbreviation: string;
    };
  };
  productSupplier: {
    id: string;
    supplier: {
      id: string;
      name: string;
      contactPerson: string | null;
    };
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export default function BatchesPage() {
  const router = useRouter();
  const [batches, setBatches] = useState<StockBatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState("receivedDate");
  const [sortOrder, setSortOrder] = useState("desc");
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  });

  // Fetch batches
  const fetchBatches = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/inventory/stock-batches?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch batches");
      }

      setBatches(data.batches);
      setPagination(data.pagination);
    } catch (err) {
      console.error("Error fetching batches:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch batches");
      toast.error("Failed to fetch batches");
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchBatches();
  }, [searchTerm, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchBatches(1);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchBatches(page);
  };

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case "EXPIRED":
        return <Badge variant="destructive">Expired</Badge>;
      case "RECALLED":
        return <Badge variant="destructive">Recalled</Badge>;
      case "SOLD_OUT":
        return <Badge variant="secondary">Sold Out</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Check if batch is expiring soon (within 30 days)
  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    return expiry <= thirtyDaysFromNow && expiry > now;
  };

  // Check if batch is expired
  const isExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) <= new Date();
  };

  return (
    <MainLayout>
      <PageHeader
        title="Batch Tracking"
        description="Manage and track inventory batches for enhanced traceability"
        actions={
          <Button asChild>
            <Link href="/inventory/batches/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Batch
            </Link>
          </Button>
        }
      />

      <div className="space-y-6">
        {/* Search and Filters */}
        <Card>
          <CardHeader>
            <CardTitle>Search & Filter</CardTitle>
            <CardDescription>Find batches by product, supplier, or batch number</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="flex gap-4 items-end">
              <div className="flex-1">
                <label htmlFor="search" className="block text-sm font-medium mb-2">
                  Search
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="Search by product name, SKU, batch number, or supplier..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-48">
                <label htmlFor="status" className="block text-sm font-medium mb-2">
                  Status
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="EXPIRED">Expired</SelectItem>
                    <SelectItem value="RECALLED">Recalled</SelectItem>
                    <SelectItem value="SOLD_OUT">Sold Out</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-48">
                <label htmlFor="sort" className="block text-sm font-medium mb-2">
                  Sort By
                </label>
                <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
                  const [field, order] = value.split('-');
                  setSortBy(field);
                  setSortOrder(order);
                }}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="receivedDate-desc">Newest First</SelectItem>
                    <SelectItem value="receivedDate-asc">Oldest First</SelectItem>
                    <SelectItem value="expiryDate-asc">Expiry Date (Soon)</SelectItem>
                    <SelectItem value="expiryDate-desc">Expiry Date (Late)</SelectItem>
                    <SelectItem value="product.name-asc">Product A-Z</SelectItem>
                    <SelectItem value="product.name-desc">Product Z-A</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button type="submit">Search</Button>
            </form>
          </CardContent>
        </Card>

        {/* Batches Table */}
        <Card>
          <CardHeader>
            <CardTitle>Stock Batches</CardTitle>
            <CardDescription>
              {pagination.total > 0 
                ? `Showing ${batches.length} of ${pagination.total} batches`
                : "No batches found"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600">{error}</p>
                <Button onClick={() => fetchBatches()} className="mt-4">
                  Try Again
                </Button>
              </div>
            ) : batches.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Batch Number</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Received</TableHead>
                      <TableHead>Expiry</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {batches.map((batch) => (
                      <TableRow key={batch.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{batch.product.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {batch.product.sku}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {batch.batchNumber || "N/A"}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {batch.productSupplier.supplier.name}
                            </div>
                            {batch.productSupplier.supplier.contactPerson && (
                              <div className="text-sm text-muted-foreground">
                                {batch.productSupplier.supplier.contactPerson}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {batch.remainingQuantity} / {batch.quantity}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {batch.product.unit.abbreviation}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(batch.receivedDate).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {batch.expiryDate ? (
                              <>
                                <div className="text-sm">
                                  {new Date(batch.expiryDate).toLocaleDateString()}
                                </div>
                                {isExpired(batch.expiryDate) && (
                                  <AlertTriangle className="h-4 w-4 text-red-500" />
                                )}
                                {isExpiringSoon(batch.expiryDate) && !isExpired(batch.expiryDate) && (
                                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                )}
                              </>
                            ) : (
                              <span className="text-sm text-muted-foreground">No expiry</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(batch.status)}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              asChild
                            >
                              <Link href={`/inventory/batches/${batch.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              asChild
                            >
                              <Link href={`/inventory/batches/${batch.id}/edit`}>
                                <Edit className="h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="mt-6">
                    <CustomPagination
                      currentPage={pagination.page}
                      totalPages={pagination.pages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No batches found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by creating a new batch.
                </p>
                <div className="mt-6">
                  <Button asChild>
                    <Link href="/inventory/batches/new">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Batch
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
