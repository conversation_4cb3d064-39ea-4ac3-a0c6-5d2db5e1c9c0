"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Plus, Eye, AlertTriangle, Package, Hash } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import Link from "next/link";

interface StockBatch {
  id: string;
  batchNumber: string | null;
  receivedDate: string;
  expiryDate: string | null;
  quantity: number;
  remainingQuantity: number;
  purchasePrice: number;
  status: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT";
  notes: string | null;
  productSupplier: {
    id: string;
    supplier: {
      id: string;
      name: string;
      contactPerson: string | null;
    };
  };
}

interface ProductBatchesTabProps {
  productId: string;
  productName: string;
}

export function ProductBatchesTab({ productId, productName }: ProductBatchesTabProps) {
  const [batches, setBatches] = useState<StockBatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState("receivedDate");
  const [sortOrder, setSortOrder] = useState("desc");

  // Fetch batches for the product
  const fetchBatches = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        sortBy,
        sortOrder,
      });

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      const response = await fetch(`/api/products/${productId}/batches?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch batches");
      }

      setBatches(data.batches || []);
    } catch (err) {
      console.error("Error fetching batches:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch batches");
      toast.error("Failed to fetch batches");
    } finally {
      setLoading(false);
    }
  };

  // Initial load and when filters change
  useEffect(() => {
    fetchBatches();
  }, [productId, statusFilter, sortBy, sortOrder]);

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case "EXPIRED":
        return <Badge variant="destructive">Expired</Badge>;
      case "RECALLED":
        return <Badge variant="destructive">Recalled</Badge>;
      case "SOLD_OUT":
        return <Badge variant="secondary">Sold Out</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Check if batch is expiring soon (within 30 days)
  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    return expiry <= thirtyDaysFromNow && expiry > now;
  };

  // Check if batch is expired
  const isExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) <= new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header with filters and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-medium">Product Batches</h3>
          <p className="text-sm text-muted-foreground">
            Track inventory batches for {productName}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="EXPIRED">Expired</SelectItem>
              <SelectItem value="RECALLED">Recalled</SelectItem>
              <SelectItem value="SOLD_OUT">Sold Out</SelectItem>
            </SelectContent>
          </Select>
          <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
            const [field, order] = value.split('-');
            setSortBy(field);
            setSortOrder(order);
          }}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="receivedDate-desc">Newest First</SelectItem>
              <SelectItem value="receivedDate-asc">Oldest First</SelectItem>
              <SelectItem value="expiryDate-asc">Expiry Date (Soon)</SelectItem>
              <SelectItem value="expiryDate-desc">Expiry Date (Late)</SelectItem>
              <SelectItem value="supplier.name-asc">Supplier A-Z</SelectItem>
              <SelectItem value="supplier.name-desc">Supplier Z-A</SelectItem>
            </SelectContent>
          </Select>
          <Button asChild>
            <Link href={`/inventory/batches/new?productId=${productId}`}>
              <Plus className="h-4 w-4 mr-2" />
              Add Batch
            </Link>
          </Button>
        </div>
      </div>

      {/* Batches Table */}
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchBatches} className="mt-4">
            Try Again
          </Button>
        </div>
      ) : batches.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Batch Number</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Purchase Price</TableHead>
              <TableHead>Received</TableHead>
              <TableHead>Expiry</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {batches.map((batch) => (
              <TableRow key={batch.id}>
                <TableCell>
                  <div className="font-mono text-sm">
                    {batch.batchNumber || "N/A"}
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {batch.productSupplier.supplier.name}
                    </div>
                    {batch.productSupplier.supplier.contactPerson && (
                      <div className="text-sm text-muted-foreground">
                        {batch.productSupplier.supplier.contactPerson}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {batch.remainingQuantity} / {batch.quantity}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      remaining / total
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="font-medium">
                    {formatCurrency(batch.purchasePrice)}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {new Date(batch.receivedDate).toLocaleDateString()}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {batch.expiryDate ? (
                      <>
                        <div className="text-sm">
                          {new Date(batch.expiryDate).toLocaleDateString()}
                        </div>
                        {isExpired(batch.expiryDate) && (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                        {isExpiringSoon(batch.expiryDate) && !isExpired(batch.expiryDate) && (
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        )}
                      </>
                    ) : (
                      <span className="text-sm text-muted-foreground">No expiry</span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(batch.status)}
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    asChild
                  >
                    <Link href={`/inventory/batches/${batch.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="text-center py-8">
          <Hash className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No batches found</h3>
          <p className="mt-1 text-sm text-gray-500">
            This product doesn't have any inventory batches yet.
          </p>
          <div className="mt-6">
            <Button asChild>
              <Link href={`/inventory/batches/new?productId=${productId}`}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Batch
              </Link>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
