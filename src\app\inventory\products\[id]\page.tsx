"use client";

import { useState, useEffect, useRef } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { formatCurrency } from "@/lib/utils";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Edit,
  Loader2,
  Package,
  Tag,
  Barcode,
  DollarSign,
  ShoppingCart,
  AlertTriangle,
  Calendar,
  Info,
  ImageIcon,
  Store,
  Warehouse,
  Clock,
  User,
  Upload,
  Users,
  Hash,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { BarcodeDisplay } from "@/components/products/BarcodeDisplay";
import { SuppliersTab } from "@/components/products/SuppliersTab";
import { ProductBatchesTab } from "@/components/products/ProductBatchesTab";

interface Product {
  id: string;
  name: string;
  description?: string | null;
  sku: string;
  barcode?: string | null;
  basePrice: number;
  purchasePrice?: number | null;
  optionalPrice1?: number | null;
  optionalPrice2?: number | null;
  discountValue?: number | null;
  discountType?: "FIXED" | "PERCENTAGE" | null;
  imageUrl?: string | null;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
  } | null;
  supplier?: {
    id: string;
    name: string;
    contactPerson?: string | null;
    phone?: string | null;
    email?: string | null;
    address?: string | null;
  } | null;
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
  storeStock?: {
    quantity: number;
    minThreshold: number;
  } | null;
  warehouseStock?: {
    quantity: number;
  } | null;
  temporaryPrice?: {
    id: string;
    value: number;
    type: "FIXED" | "PERCENTAGE";
    startDate: string;
    endDate: string;
    createdAt: string;
    createdBy: string;
    user: {
      id: string;
      name: string;
    };
  } | null;
}

export default function ProductDetailPage() {
  // Use the useParams hook to get the id parameter
  const params = useParams();
  const id = params.id as string;

  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [deletingTemporaryPrice, setDeletingTemporaryPrice] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Fetch product data on component mount
  useEffect(() => {
    const fetchProduct = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/products/${id}`);

        if (!response.ok) {
          throw new Error("Failed to fetch product");
        }

        const data = await response.json();
        setProduct(data.product);
      } catch (error) {
        console.error("Error fetching product:", error);
        setError("Failed to load product. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  // Handle image upload
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      setUploadError("Please select an image file");
      return;
    }

    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError("Image size should be less than 5MB");
      return;
    }

    setUploading(true);
    setUploadError(null);

    try {
      // Create form data
      const formData = new FormData();
      formData.append("file", file);

      // Upload image
      const response = await fetch(`/api/products/${id}/image`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload image");
      }

      // Refresh the page to show the updated image
      router.refresh();

      // Fetch the product data again
      const productResponse = await fetch(`/api/products/${id}`);
      if (productResponse.ok) {
        const data = await productResponse.json();
        setProduct(data.product);
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      setUploadError((error as Error).message || "Failed to upload image");
    } finally {
      setUploading(false);
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Get stock status
  const getStockStatus = () => {
    if (!product?.storeStock) return { label: "No Stock", variant: "outline" as const };

    const quantity = Number(product.storeStock.quantity);
    const threshold = Number(product.storeStock.minThreshold);

    if (quantity <= 0) return { label: "Out of Stock", variant: "destructive" as const };
    if (quantity < threshold) return { label: "Low Stock", variant: "secondary" as const };
    return { label: "In Stock", variant: "default" as const };
  };

  // Handle deleting temporary price
  const handleDeleteTemporaryPrice = async () => {
    if (!product) return;

    setDeletingTemporaryPrice(true);

    try {
      const response = await fetch(`/api/products/${id}/temporary-price`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete temporary price");
      }

      // Refresh the product data
      const productResponse = await fetch(`/api/products/${id}`);
      if (productResponse.ok) {
        const data = await productResponse.json();
        setProduct(data.product);
      }
    } catch (error) {
      console.error("Error deleting temporary price:", error);
      setError((error as Error).message || "Failed to delete temporary price");
    } finally {
      setDeletingTemporaryPrice(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading product...</span>
        </div>
      </MainLayout>
    );
  }

  if (error || !product) {
    return (
      <MainLayout>
        <PageHeader
          title="Product Details"
          description="View product information"
          actions={
            <Button variant="outline" asChild>
              <Link href="/inventory/products">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </Link>
            </Button>
          }
        />
        <Alert variant="destructive" className="mt-6">
          <AlertDescription>{error || "Product not found"}</AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  const stockStatus = getStockStatus();
  const formattedCreatedAt = new Date(product.createdAt).toLocaleDateString();
  const formattedUpdatedAt = new Date(product.updatedAt).toLocaleDateString();

  return (
    <MainLayout>
      <PageHeader
        title={product.name}
        description={`Product details and information`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/inventory/products">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Link>
            </Button>
            <Button asChild>
              <Link href={`/inventory/products/edit/${product.id}`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
          </div>
        }
      />

      <div className="mt-6">
        <Tabs defaultValue="details" className="space-y-6">
          <TabsList>
            <TabsTrigger value="details">
              <Package className="h-4 w-4 mr-2" />
              Product Details
            </TabsTrigger>
            <TabsTrigger value="suppliers">
              <Users className="h-4 w-4 mr-2" />
              Suppliers
            </TabsTrigger>
            <TabsTrigger value="batches">
              <Hash className="h-4 w-4 mr-2" />
              Batches
            </TabsTrigger>
          </TabsList>

          <TabsContent value="details">
            <Card>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Product Image and Basic Info */}
                  <div className="flex flex-col gap-4">
                    <div className="relative aspect-square w-full rounded-md border overflow-hidden bg-muted">
                      {product.imageUrl ? (
                        <Image
                          src={product.imageUrl}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <button
                          onClick={triggerFileInput}
                          className="flex h-full w-full items-center justify-center bg-muted hover:bg-muted/80 transition-colors cursor-pointer"
                          disabled={uploading}
                        >
                          {uploading ? (
                            <div className="flex flex-col items-center gap-2">
                              <Loader2 className="h-10 w-10 animate-spin text-primary" />
                              <span className="text-xs text-muted-foreground">Uploading...</span>
                            </div>
                          ) : (
                            <div className="flex flex-col items-center gap-2">
                              <Upload className="h-10 w-10 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                Click to upload image
                              </span>
                            </div>
                          )}
                        </button>
                      )}
                      {/* Hidden file input */}
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleImageUpload}
                        accept="image/*"
                        className="hidden"
                      />
                    </div>
                    {uploadError && (
                      <div className="text-xs text-destructive mt-1 flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {uploadError}
                      </div>
                    )}
                    <div className="flex flex-col gap-2">
                      <div className="flex justify-between items-center">
                        <Badge
                          variant={product.active ? "default" : "secondary"}
                          className="text-xs"
                        >
                          {product.active ? "Active" : "Inactive"}
                        </Badge>
                        <Badge variant={stockStatus.variant} className="text-xs">
                          {stockStatus.label}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1 text-sm">
                        <Tag className="h-3.5 w-3.5 text-muted-foreground" />
                        <span className="font-medium">SKU:</span>
                        <span className="ml-1">{product.sku}</span>
                      </div>
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-1 text-sm">
                          <Barcode className="h-3.5 w-3.5 text-muted-foreground" />
                          <span className="font-medium">Barcode:</span>
                          <span className="ml-1">{product.barcode || "-"}</span>
                        </div>
                        {product.barcode && (
                          <div className="mt-2">
                            <BarcodeDisplay
                              value={product.barcode}
                              height={40}
                              width={1.5}
                              fontSize={10}
                              margin={0}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Product Details */}
                  <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                    {/* Left Column */}
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium flex items-center mb-2">
                          <Package className="h-4 w-4 mr-1" />
                          Product Details
                        </h3>
                        <Separator className="mb-3" />
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <span className="text-muted-foreground">Category:</span>
                          <span>{product.category?.name || "Uncategorized"}</span>

                          <span className="text-muted-foreground">Unit:</span>
                          <span>
                            {product.unit.name} ({product.unit.abbreviation})
                          </span>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium flex items-center mb-2">
                          <User className="h-4 w-4 mr-1" />
                          Supplier Information
                        </h3>
                        <Separator className="mb-3" />
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <span className="text-muted-foreground">Supplier:</span>
                          <span>{product.supplier?.name || "-"}</span>

                          <span className="text-muted-foreground">Contact Person:</span>
                          <span>{product.supplier?.contactPerson || "-"}</span>

                          <span className="text-muted-foreground">Phone:</span>
                          <span>{product.supplier?.phone || "-"}</span>

                          <span className="text-muted-foreground">Email:</span>
                          <span>{product.supplier?.email || "-"}</span>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium flex items-center mb-2">
                          <DollarSign className="h-4 w-4 mr-1" />
                          Pricing
                        </h3>
                        <Separator className="mb-3" />
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <span className="text-muted-foreground">Base Price:</span>
                          <span className="font-medium">
                            {formatCurrency(Number(product.basePrice))}
                          </span>

                          <span className="text-muted-foreground">Optional Price 1:</span>
                          <span>
                            {product.optionalPrice1
                              ? formatCurrency(Number(product.optionalPrice1))
                              : "-"}
                          </span>

                          <span className="text-muted-foreground">Optional Price 2:</span>
                          <span>
                            {product.optionalPrice2
                              ? formatCurrency(Number(product.optionalPrice2))
                              : "-"}
                          </span>

                          <span className="text-muted-foreground">Purchase Price:</span>
                          <span>
                            {product.purchasePrice
                              ? formatCurrency(Number(product.purchasePrice))
                              : "-"}
                          </span>

                          <span className="text-muted-foreground">Discount:</span>
                          <span>
                            {product.discountValue
                              ? product.discountType === "PERCENTAGE"
                                ? `${product.discountValue}%`
                                : formatCurrency(Number(product.discountValue))
                              : "-"}
                          </span>

                          {product.temporaryPrice && (
                            <>
                              <span className="col-span-2 pt-2 font-medium text-green-600">
                                Temporary Discount:
                              </span>
                              <span className="text-muted-foreground">Type:</span>
                              <span>
                                {product.temporaryPrice.type === "PERCENTAGE"
                                  ? "Percentage"
                                  : "Fixed Amount"}
                              </span>

                              <span className="text-muted-foreground">Value:</span>
                              <span className="text-green-600 font-medium">
                                {product.temporaryPrice.type === "PERCENTAGE"
                                  ? `${product.temporaryPrice.value}%`
                                  : formatCurrency(Number(product.temporaryPrice.value))}
                              </span>

                              <span className="text-muted-foreground">Effective Price:</span>
                              <span className="text-green-600 font-medium">
                                {product.temporaryPrice.type === "PERCENTAGE"
                                  ? formatCurrency(
                                      Number(product.basePrice) -
                                        (Number(product.basePrice) *
                                          Number(product.temporaryPrice.value)) /
                                          100
                                    )
                                  : formatCurrency(Number(product.temporaryPrice.value))}
                              </span>

                              <span className="text-muted-foreground">Valid From:</span>
                              <span>
                                {new Date(product.temporaryPrice.startDate).toLocaleDateString()}
                              </span>

                              <span className="text-muted-foreground">Valid Until:</span>
                              <span>
                                {new Date(product.temporaryPrice.endDate).toLocaleDateString()}
                              </span>

                              <span className="text-muted-foreground">Created By:</span>
                              <span>{product.temporaryPrice.user?.name || "-"}</span>

                              <span className="col-span-2 mt-2">
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={handleDeleteTemporaryPrice}
                                  disabled={deletingTemporaryPrice}
                                  className="w-full"
                                >
                                  {deletingTemporaryPrice ? (
                                    <>
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                      Removing...
                                    </>
                                  ) : (
                                    "Remove Temporary Discount"
                                  )}
                                </Button>
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-sm font-medium flex items-center mb-2">
                          <Store className="h-4 w-4 mr-1" />
                          Store Stock
                        </h3>
                        <Separator className="mb-3" />
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <span className="text-muted-foreground">Quantity:</span>
                          <span>
                            {product.storeStock
                              ? `${Number(product.storeStock.quantity)} ${product.unit.abbreviation}`
                              : "-"}
                          </span>

                          <span className="text-muted-foreground">Min Threshold:</span>
                          <span>
                            {product.storeStock
                              ? `${Number(product.storeStock.minThreshold)} ${product.unit.abbreviation}`
                              : "-"}
                          </span>
                        </div>
                        {product.storeStock &&
                          Number(product.storeStock.quantity) <
                            Number(product.storeStock.minThreshold) && (
                            <div className="mt-2 flex items-center text-amber-600 text-xs">
                              <AlertTriangle className="h-3.5 w-3.5 mr-1" />
                              <span>Stock is below the minimum threshold</span>
                            </div>
                          )}
                      </div>

                      <div>
                        <h3 className="text-sm font-medium flex items-center mb-2">
                          <Warehouse className="h-4 w-4 mr-1" />
                          Warehouse Stock
                        </h3>
                        <Separator className="mb-3" />
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <span className="text-muted-foreground">Quantity:</span>
                          <span>
                            {product.warehouseStock
                              ? `${Number(product.warehouseStock.quantity)} ${product.unit.abbreviation}`
                              : "-"}
                          </span>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium flex items-center mb-2">
                          <Clock className="h-4 w-4 mr-1" />
                          Timestamps
                        </h3>
                        <Separator className="mb-3" />
                        <div className="grid grid-cols-2 gap-y-2 text-sm">
                          <span className="text-muted-foreground">Created:</span>
                          <span>{formattedCreatedAt}</span>

                          <span className="text-muted-foreground">Last Updated:</span>
                          <span>{formattedUpdatedAt}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Description Section */}
                  <div className="mt-6">
                    <h3 className="text-sm font-medium flex items-center mb-2">
                      <Info className="h-4 w-4 mr-1" />
                      Description
                    </h3>
                    <Separator className="mb-3" />
                    <p className="text-sm text-muted-foreground">
                      {product.description || "No description provided."}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suppliers">
            <Card>
              <CardContent className="p-6">
                <SuppliersTab productId={product.id} productName={product.name} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="batches">
            <Card>
              <CardContent className="p-6">
                <ProductBatchesTab productId={product.id} productName={product.name} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
