"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Loader2, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface StockBatch {
  id: string;
  batchNumber: string | null;
  expiryDate: string | null;
  purchasePrice: number;
  status: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT";
  notes: string | null;
  product: {
    id: string;
    name: string;
    sku: string;
    unit: {
      name: string;
      abbreviation: string;
    };
  };
  productSupplier: {
    supplier: {
      name: string;
    };
  };
}

interface FormData {
  batchNumber: string;
  expiryDate: Date | null;
  purchasePrice: string;
  status: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT";
  notes: string;
}

export default function EditBatchPage() {
  const router = useRouter();
  const params = useParams();
  const batchId = params.id as string;
  
  const [batch, setBatch] = useState<StockBatch | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({
    batchNumber: "",
    expiryDate: null,
    purchasePrice: "",
    status: "ACTIVE",
    notes: "",
  });

  // Fetch batch details
  useEffect(() => {
    const fetchBatch = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/inventory/stock-batches/${batchId}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch batch details");
        }

        const batchData = data.batch;
        setBatch(batchData);
        
        // Populate form with existing data
        setFormData({
          batchNumber: batchData.batchNumber || "",
          expiryDate: batchData.expiryDate ? new Date(batchData.expiryDate) : null,
          purchasePrice: batchData.purchasePrice.toString(),
          status: batchData.status,
          notes: batchData.notes || "",
        });
      } catch (err) {
        console.error("Error fetching batch:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch batch details");
        toast.error("Failed to fetch batch details");
      } finally {
        setLoading(false);
      }
    };

    if (batchId) {
      fetchBatch();
    }
  }, [batchId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.purchasePrice || parseFloat(formData.purchasePrice) <= 0) {
      toast.error("Please enter a valid purchase price");
      return;
    }

    try {
      setSaving(true);

      const payload = {
        batchNumber: formData.batchNumber || null,
        expiryDate: formData.expiryDate?.toISOString() || null,
        purchasePrice: parseFloat(formData.purchasePrice),
        status: formData.status,
        notes: formData.notes || null,
      };

      const response = await fetch(`/api/inventory/stock-batches/${batchId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Batch updated successfully");
        router.push(`/inventory/batches/${batchId}`);
      } else {
        throw new Error(data.error || "Failed to update batch");
      }
    } catch (error) {
      console.error("Error updating batch:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update batch");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (error || !batch) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <p className="text-red-600">{error || "Batch not found"}</p>
          <Button asChild className="mt-4">
            <Link href="/inventory/batches">Back to Batches</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Edit Batch"
        description={`${batch.product.name} - ${batch.batchNumber || "No batch number"}`}
        actions={
          <Button variant="outline" asChild>
            <Link href={`/inventory/batches/${batchId}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Details
            </Link>
          </Button>
        }
      />

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Edit Batch Information</CardTitle>
            <CardDescription>
              Update the batch details. Note: Product and supplier cannot be changed.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Read-only Product Information */}
            <div className="mb-6 p-4 bg-muted rounded-lg">
              <h3 className="font-medium mb-2">Product Information (Read-only)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Product:</span>
                  <p className="font-medium">{batch.product.name}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">SKU:</span>
                  <p className="font-mono">{batch.product.sku}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Supplier:</span>
                  <p className="font-medium">{batch.productSupplier.supplier.name}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Unit:</span>
                  <p>{batch.product.unit.name} ({batch.product.unit.abbreviation})</p>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Batch Number */}
              <div className="space-y-2">
                <Label htmlFor="batchNumber">Batch Number</Label>
                <Input
                  id="batchNumber"
                  placeholder="Enter batch number (optional)"
                  value={formData.batchNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, batchNumber: e.target.value }))}
                />
              </div>

              {/* Purchase Price */}
              <div className="space-y-2">
                <Label htmlFor="purchasePrice">Purchase Price *</Label>
                <Input
                  id="purchasePrice"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="Enter purchase price per unit"
                  value={formData.purchasePrice}
                  onChange={(e) => setFormData(prev => ({ ...prev, purchasePrice: e.target.value }))}
                  required
                />
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT") => 
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="EXPIRED">Expired</SelectItem>
                    <SelectItem value="RECALLED">Recalled</SelectItem>
                    <SelectItem value="SOLD_OUT">Sold Out</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Expiry Date */}
              <div className="space-y-2">
                <Label>Expiry Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.expiryDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.expiryDate ? format(formData.expiryDate, "PPP") : "Pick expiry date (optional)"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.expiryDate || undefined}
                      onSelect={(date) => setFormData(prev => ({ ...prev, expiryDate: date || null }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, expiryDate: null }))}
                  >
                    Clear Date
                  </Button>
                </div>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Enter any additional notes about this batch"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" asChild>
                  <Link href={`/inventory/batches/${batchId}`}>Cancel</Link>
                </Button>
                <Button type="submit" disabled={saving}>
                  {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Update Batch
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
