import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { BatchStatus } from "@/generated/prisma";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/stock-batches - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/stock-batches - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Validation schema for creating a new stock batch
const createBatchSchema = z.object({
  productId: z.string({
    required_error: "Product ID is required",
  }),
  productSupplierId: z.string({
    required_error: "Product Supplier ID is required",
  }),
  batchNumber: z.string().optional().nullable(),
  receivedDate: z.string().datetime().optional(),
  expiryDate: z.string().datetime().optional().nullable(),
  quantity: z.number().positive({
    message: "Quantity must be positive",
  }),
  purchasePrice: z.number().positive({
    message: "Purchase price must be positive",
  }),
  purchaseOrderId: z.string().optional().nullable(),
  warehouseStockId: z.string().optional().nullable(),
  storeStockId: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

// GET /api/inventory/stock-batches - Get all stock batches with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    console.log("[API] GET /api/inventory/stock-batches - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const productId = searchParams.get("productId");
    const supplierId = searchParams.get("supplierId");
    const status = searchParams.get("status") as BatchStatus | null;
    const search = searchParams.get("search");
    const sortBy = searchParams.get("sortBy") || "receivedDate";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    console.log("[API] Query parameters:", {
      page,
      limit,
      productId,
      supplierId,
      status,
      search,
      sortBy,
      sortOrder
    });

    // Build where clause
    const where: any = {};

    if (productId) {
      where.productId = productId;
    }

    if (supplierId) {
      where.productSupplier = {
        supplierId: supplierId
      };
    }

    if (status) {
      where.status = status;
    }

    if (search) {
      where.OR = [
        {
          batchNumber: {
            contains: search,
            mode: "insensitive"
          }
        },
        {
          product: {
            name: {
              contains: search,
              mode: "insensitive"
            }
          }
        },
        {
          product: {
            sku: {
              contains: search,
              mode: "insensitive"
            }
          }
        },
        {
          productSupplier: {
            supplier: {
              name: {
                contains: search,
                mode: "insensitive"
              }
            }
          }
        }
      ];
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build order by clause
    const orderBy: any = {};
    if (sortBy === "product.name") {
      orderBy.product = { name: sortOrder };
    } else if (sortBy === "supplier.name") {
      orderBy.productSupplier = { supplier: { name: sortOrder } };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    console.log("[API] Fetching stock batches with filters:", { where, orderBy, skip, limit });

    // Get stock batches with pagination
    const [batches, total] = await Promise.all([
      prisma.stockBatch.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              barcode: true,
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true
                }
              }
            }
          },
          productSupplier: {
            include: {
              supplier: {
                select: {
                  id: true,
                  name: true,
                  contactPerson: true,
                  phone: true,
                  email: true
                }
              }
            }
          },
          purchaseOrder: {
            select: {
              id: true,
              orderDate: true,
              status: true
            }
          },
          storeStock: {
            select: {
              id: true,
              quantity: true
            }
          },
          warehouseStock: {
            select: {
              id: true,
              quantity: true
            }
          }
        },
        skip,
        take: limit,
        orderBy,
      }),
      prisma.stockBatch.count({ where }),
    ]);

    console.log("[API] Found", batches.length, "batches out of", total, "total");

    return NextResponse.json({
      batches,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("[API] Error fetching stock batches:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock batches", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/stock-batches - Create a new stock batch
export async function POST(request: NextRequest) {
  try {
    console.log("[API] POST /api/inventory/stock-batches - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create stock batches
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log("[API] Request body:", body);

    // Validate batch data
    const validationResult = createBatchSchema.safeParse(body);
    if (!validationResult.success) {
      console.error("[API] Validation failed:", validationResult.error.issues);
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const {
      productId,
      productSupplierId,
      batchNumber,
      receivedDate,
      expiryDate,
      quantity,
      purchasePrice,
      purchaseOrderId,
      warehouseStockId,
      storeStockId,
      notes
    } = validationResult.data;

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Verify product supplier relationship exists
    const productSupplier = await prisma.productSupplier.findUnique({
      where: { id: productSupplierId },
      include: {
        supplier: {
          select: { id: true, name: true }
        }
      }
    });

    if (!productSupplier) {
      return NextResponse.json(
        { error: "Product supplier relationship not found" },
        { status: 404 }
      );
    }

    // Verify the product supplier relationship matches the product
    if (productSupplier.productId !== productId) {
      return NextResponse.json(
        { error: "Product supplier relationship does not match the specified product" },
        { status: 400 }
      );
    }

    // Verify purchase order exists if provided
    if (purchaseOrderId) {
      const purchaseOrder = await prisma.purchaseOrder.findUnique({
        where: { id: purchaseOrderId },
        select: { id: true, status: true }
      });

      if (!purchaseOrder) {
        return NextResponse.json(
          { error: "Purchase order not found" },
          { status: 404 }
        );
      }
    }

    // Verify stock location exists if provided
    if (warehouseStockId) {
      const warehouseStock = await prisma.warehouseStock.findUnique({
        where: { id: warehouseStockId },
        select: { id: true, productId: true }
      });

      if (!warehouseStock || warehouseStock.productId !== productId) {
        return NextResponse.json(
          { error: "Warehouse stock not found or does not match product" },
          { status: 404 }
        );
      }
    }

    if (storeStockId) {
      const storeStock = await prisma.storeStock.findUnique({
        where: { id: storeStockId },
        select: { id: true, productId: true }
      });

      if (!storeStock || storeStock.productId !== productId) {
        return NextResponse.json(
          { error: "Store stock not found or does not match product" },
          { status: 404 }
        );
      }
    }

    console.log("[API] Creating new stock batch");

    // Create the stock batch
    const newBatch = await prisma.stockBatch.create({
      data: {
        productId,
        productSupplierId,
        batchNumber,
        receivedDate: receivedDate ? new Date(receivedDate) : new Date(),
        expiryDate: expiryDate ? new Date(expiryDate) : null,
        quantity,
        remainingQuantity: quantity, // Initially, remaining quantity equals total quantity
        purchasePrice,
        purchaseOrderId,
        warehouseStockId,
        storeStockId,
        notes,
        status: BatchStatus.ACTIVE
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true
              }
            }
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true
              }
            }
          }
        }
      }
    });

    console.log("[API] Stock batch created successfully:", newBatch.id);

    return NextResponse.json({
      message: "Stock batch created successfully",
      batch: newBatch
    }, { status: 201 });

  } catch (error) {
    console.error("[API] Error creating stock batch:", error);
    return NextResponse.json(
      { error: "Failed to create stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}
