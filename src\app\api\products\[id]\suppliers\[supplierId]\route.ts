import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";

// ProductSupplier update schema for validation
const productSupplierUpdateSchema = z.object({
  supplierProductCode: z.string().optional(),
  supplierProductName: z.string().optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }).optional(),
  minimumOrderQuantity: z.number().positive().optional(),
  leadTimeDays: z.number().int().min(0).optional(),
  isPreferred: z.boolean().optional(),
  isActive: z.boolean().optional(),
  notes: z.string().optional(),
});

// Partial update schema for PATCH requests (allows fewer fields)
const productSupplierPatchSchema = z.object({
  supplierProductCode: z.string().optional(),
  supplierProductName: z.string().optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }).optional(),
  minimumOrderQuantity: z.number().positive().optional(),
  leadTimeDays: z.number().int().min(0).optional(),
  isPreferred: z.boolean().optional(),
  isActive: z.boolean().optional(),
  notes: z.string().optional(),
}).refine((data) => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update",
});

// GET /api/products/[id]/suppliers/[supplierId] - Get specific product-supplier relationship
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; supplierId: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { id: productId, supplierId } = params;

    // Get the product-supplier relationship
    const productSupplier = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            basePrice: true,
          }
        },
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
            address: true,
          }
        },
        purchaseOrderItems: {
          include: {
            purchaseOrder: {
              select: {
                id: true,
                orderDate: true,
                status: true,
                total: true,
              }
            }
          },
          orderBy: {
            purchaseOrder: {
              orderDate: 'desc'
            }
          },
          take: 5 // Last 5 purchase orders
        },
        stockBatches: {
          where: {
            status: 'ACTIVE'
          },
          select: {
            id: true,
            batchNumber: true,
            receivedDate: true,
            expiryDate: true,
            quantity: true,
            remainingQuantity: true,
            purchasePrice: true,
          },
          orderBy: {
            receivedDate: 'desc'
          },
          take: 10 // Last 10 batches
        }
      }
    });

    if (!productSupplier) {
      return NextResponse.json(
        { error: "Product-supplier relationship not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ productSupplier });
  } catch (error) {
    console.error("Error fetching product-supplier relationship:", error);
    return NextResponse.json(
      { error: "Failed to fetch product-supplier relationship", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PUT /api/products/[id]/suppliers/[supplierId] - Update product-supplier relationship
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; supplierId: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id: productId, supplierId } = params;

    // Verify relationship exists
    const existingRelationship = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      include: {
        product: { select: { name: true } },
        supplier: { select: { name: true } }
      }
    });

    if (!existingRelationship) {
      return NextResponse.json(
        { error: "Product-supplier relationship not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate data
    const validationResult = productSupplierUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // If setting as preferred, unset other preferred suppliers for this product
    if (data.isPreferred === true) {
      await prisma.productSupplier.updateMany({
        where: {
          productId,
          isPreferred: true,
          id: { not: existingRelationship.id } // Exclude current relationship
        },
        data: {
          isPreferred: false
        }
      });
    }

    // Update the relationship
    const updatedProductSupplier = await prisma.productSupplier.update({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      data: {
        ...data,
        lastPurchasePrice: data.purchasePrice ? existingRelationship.purchasePrice : existingRelationship.lastPurchasePrice,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          }
        },
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          }
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_PRODUCT_SUPPLIER",
        details: `Updated supplier relationship: ${existingRelationship.supplier.name} for product ${existingRelationship.product.name}`,
      },
    });

    return NextResponse.json({
      message: "Product-supplier relationship updated successfully",
      productSupplier: updatedProductSupplier
    });
  } catch (error) {
    console.error("Error updating product-supplier relationship:", error);
    return NextResponse.json(
      { error: "Failed to update product-supplier relationship", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/products/[id]/suppliers/[supplierId] - Remove supplier from product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; supplierId: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id: productId, supplierId } = params;

    // Verify relationship exists
    const existingRelationship = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      include: {
        product: { select: { name: true } },
        supplier: { select: { name: true } },
        stockBatches: { select: { id: true } },
        purchaseOrderItems: { select: { id: true } }
      }
    });

    if (!existingRelationship) {
      return NextResponse.json(
        { error: "Product-supplier relationship not found" },
        { status: 404 }
      );
    }

    // Check if there are active stock batches or purchase order items
    if (existingRelationship.stockBatches.length > 0 || existingRelationship.purchaseOrderItems.length > 0) {
      return NextResponse.json(
        { 
          error: "Cannot delete supplier relationship", 
          message: "This supplier has associated stock batches or purchase order items. Please deactivate instead of deleting." 
        },
        { status: 409 }
      );
    }

    // Delete the relationship
    await prisma.productSupplier.delete({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_PRODUCT_SUPPLIER",
        details: `Removed supplier ${existingRelationship.supplier.name} from product ${existingRelationship.product.name}`,
      },
    });

    return NextResponse.json({
      message: "Supplier removed from product successfully"
    });
  } catch (error) {
    console.error("Error removing supplier from product:", error);
    return NextResponse.json(
      { error: "Failed to remove supplier from product", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/products/[id]/suppliers/[supplierId] - Partially update product-supplier relationship
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string; supplierId: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id: productId, supplierId } = params;

    // Get request body
    const body = await request.json();

    // Validate data using patch schema
    const validationResult = productSupplierPatchSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    // Verify relationship exists
    const existingRelationship = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      include: {
        product: { select: { name: true } },
        supplier: { select: { name: true } }
      }
    });

    if (!existingRelationship) {
      return NextResponse.json(
        { error: "Product-supplier relationship not found" },
        { status: 404 }
      );
    }

    const data = validationResult.data;

    // If setting as preferred, unset other preferred suppliers for this product
    if (data.isPreferred === true) {
      await prisma.productSupplier.updateMany({
        where: {
          productId,
          isPreferred: true,
          id: { not: existingRelationship.id } // Exclude current relationship
        },
        data: {
          isPreferred: false
        }
      });
    }

    // Update the relationship with only provided fields
    const updatedProductSupplier = await prisma.productSupplier.update({
      where: {
        productId_supplierId: {
          productId,
          supplierId
        }
      },
      data: {
        ...data,
        // Only update lastPurchasePrice if purchasePrice is being updated
        ...(data.purchasePrice && { lastPurchasePrice: existingRelationship.purchasePrice }),
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          }
        },
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          }
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "PATCH_PRODUCT_SUPPLIER",
        details: `Partially updated supplier relationship: ${existingRelationship.supplier.name} for product ${existingRelationship.product.name}`,
      },
    });

    return NextResponse.json({
      message: "Product-supplier relationship updated successfully",
      productSupplier: updatedProductSupplier
    });
  } catch (error) {
    console.error("Error partially updating product-supplier relationship:", error);
    return NextResponse.json(
      { error: "Failed to update product-supplier relationship", message: (error as Error).message },
      { status: 500 }
    );
  }
}
